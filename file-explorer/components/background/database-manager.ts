// components/background/database-manager.ts
import Database from 'better-sqlite3';
import { join } from 'path';
import { app } from 'electron';

export interface DatabaseConfig {
  dbPath?: string;
  enableWAL?: boolean;
  enableForeignKeys?: boolean;
  timeout?: number;
}

export interface Migration {
  version: number;
  name: string;
  up: string;
  down: string;
}

export class DatabaseManager {
  private db: Database.Database | null = null;
  private config: DatabaseConfig;
  private migrations: Migration[] = [];

  constructor(config: DatabaseConfig = {}) {
    this.config = {
      dbPath: config.dbPath || this.getDefaultDbPath(),
      enableWAL: config.enableWAL ?? true,
      enableForeignKeys: config.enableForeignKeys ?? true,
      timeout: config.timeout ?? 30000,
      ...config
    };
  }

  private getDefaultDbPath(): string {
    try {
      // In Electron main process
      const userDataPath = app.getPath('userData');
      return join(userDataPath, 'synapse.db');
    } catch (error) {
      // Fallback for renderer process or non-Electron environment
      return join(process.cwd(), 'data', 'synapse.db');
    }
  }

  public async initialize(): Promise<void> {
    if (this.db) {
      return;
    }

    try {
      this.db = new Database(this.config.dbPath!);
      
      // Configure database
      if (this.config.enableWAL) {
        this.db.pragma('journal_mode = WAL');
      }
      
      if (this.config.enableForeignKeys) {
        this.db.pragma('foreign_keys = ON');
      }

      this.db.pragma(`busy_timeout = ${this.config.timeout}`);

      // Initialize schema version table
      this.initializeSchemaVersion();

      // Run migrations
      await this.runMigrations();

      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private initializeSchemaVersion(): void {
    if (!this.db) throw new Error('Database not initialized');

    this.db.exec(`
      CREATE TABLE IF NOT EXISTS schema_version (
        version INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);
  }

  public addMigration(migration: Migration): void {
    this.migrations.push(migration);
    this.migrations.sort((a, b) => a.version - b.version);
  }

  public addMigrations(migrations: Migration[]): void {
    migrations.forEach(migration => this.addMigration(migration));
  }

  private async runMigrations(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const getCurrentVersion = this.db.prepare(`
      SELECT MAX(version) as version FROM schema_version
    `);

    const currentVersionResult = getCurrentVersion.get() as { version: number | null };
    const currentVersion = currentVersionResult?.version || 0;

    const pendingMigrations = this.migrations.filter(m => m.version > currentVersion);

    if (pendingMigrations.length === 0) {
      console.log('No pending migrations');
      return;
    }

    console.log(`Running ${pendingMigrations.length} migrations...`);

    const insertVersion = this.db.prepare(`
      INSERT INTO schema_version (version, name) VALUES (?, ?)
    `);

    for (const migration of pendingMigrations) {
      try {
        console.log(`Applying migration ${migration.version}: ${migration.name}`);
        
        this.db.transaction(() => {
          this.db!.exec(migration.up);
          insertVersion.run(migration.version, migration.name);
        })();

        console.log(`Migration ${migration.version} applied successfully`);
      } catch (error) {
        console.error(`Failed to apply migration ${migration.version}:`, error);
        throw error;
      }
    }
  }

  public query<T = any>(sql: string, params: any[] = []): T[] {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      const stmt = this.db.prepare(sql);
      return stmt.all(...params) as T[];
    } catch (error) {
      console.error('Query failed:', sql, params, error);
      throw error;
    }
  }

  public queryOne<T = any>(sql: string, params: any[] = []): T | null {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      const stmt = this.db.prepare(sql);
      return (stmt.get(...params) as T) || null;
    } catch (error) {
      console.error('Query failed:', sql, params, error);
      throw error;
    }
  }

  public execute(sql: string, params: any[] = []): Database.RunResult {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      const stmt = this.db.prepare(sql);
      return stmt.run(...params);
    } catch (error) {
      console.error('Execute failed:', sql, params, error);
      throw error;
    }
  }

  public transaction<T>(fn: () => T): T {
    if (!this.db) throw new Error('Database not initialized');
    
    return this.db.transaction(fn)();
  }

  public prepare(sql: string): Database.Statement {
    if (!this.db) throw new Error('Database not initialized');
    
    return this.db.prepare(sql);
  }

  public close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('Database connection closed');
    }
  }

  public isInitialized(): boolean {
    return this.db !== null;
  }

  public getDbPath(): string {
    return this.config.dbPath!;
  }

  // Utility methods for common operations
  public async backup(backupPath: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      await this.db.backup(backupPath);
      console.log(`Database backed up to: ${backupPath}`);
    } catch (error) {
      console.error('Backup failed:', error);
      throw error;
    }
  }

  public vacuum(): void {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      this.db.exec('VACUUM');
      console.log('Database vacuumed successfully');
    } catch (error) {
      console.error('Vacuum failed:', error);
      throw error;
    }
  }

  public analyze(): void {
    if (!this.db) throw new Error('Database not initialized');
    
    try {
      this.db.exec('ANALYZE');
      console.log('Database analyzed successfully');
    } catch (error) {
      console.error('Analyze failed:', error);
      throw error;
    }
  }
}

// Singleton instance
let dbManager: DatabaseManager | null = null;

export function getDatabaseManager(config?: DatabaseConfig): DatabaseManager {
  if (!dbManager) {
    dbManager = new DatabaseManager(config);
  }
  return dbManager;
}

export function closeDatabaseManager(): void {
  if (dbManager) {
    dbManager.close();
    dbManager = null;
  }
}
