// components/background/index.ts

// Database Manager (Main process only)
export {
  DatabaseManager,
  getDatabaseManager,
  closeDatabaseManager,
  type DatabaseConfig,
  type Migration
} from './database-manager';

// Configuration Store (Main process only)
export {
  ConfigStore,
  getConfigStore,
} from './config-store';

// Browser-safe Configuration Store (Renderer process)
export {
  ConfigStoreBrowser,
  getConfigStoreBrowser,
  type ProjectConfig,
  type NamingConventions,
  type CodeArchitecture,
  type StyleGuide,
  type GlobalSettings
} from './config-store-browser';

// Default Configurations
export {
  DEFAULT_NAMING_CONVENTIONS,
  DEFAULT_CODE_ARCHITECTURE,
  DEFAULT_STYLE_GUIDE,
  TYPESCRIPT_NAMING_CONVENTIONS,
  REACT_CODE_ARCHITECTURE,
  NEXTJS_STYLE_GUIDE,
  PROJECT_TEMPLATES,
  getProjectTemplate,
  createDefaultProjectConfig
} from './default-configs';

// Vector Database (Browser-safe)
export {
  BasicVectorDatabase,
  type VectorDocument,
  type SearchResult,
  type VectorSearchOptions
} from './vector-database';

// Code Indexer (Browser-safe)
export {
  CodeIndexer,
  type IndexingOptions,
  type IndexingProgress,
  type FileChunk
} from './code-indexer';

// Semantic Search (Browser-safe)
export {
  SemanticSearchService,
  type SemanticSearchQuery,
  type CodeContext,
  type SearchSuggestion
} from './semantic-search';

// Background System Initialization (Browser-safe)
export async function initializeBackgroundSystems(): Promise<void> {
  try {
    console.log('Initializing background systems...');

    // Use browser-safe config store
    const configStore = getConfigStoreBrowser();
    await configStore.initialize();

    console.log('Background systems initialized successfully');
  } catch (error) {
    console.error('Failed to initialize background systems:', error);
    throw error;
  }
}

// Background System Cleanup (Browser-safe)
export function cleanupBackgroundSystems(): void {
  try {
    console.log('Cleaning up background systems...');
    // Browser version doesn't need cleanup
    console.log('Background systems cleaned up successfully');
  } catch (error) {
    console.error('Failed to cleanup background systems:', error);
  }
}

// Health Check (Browser-safe)
export async function checkBackgroundSystemsHealth(): Promise<{
  database: boolean;
  configStore: boolean;
  overall: boolean;
}> {
  const health = {
    database: false,
    configStore: false,
    overall: false
  };

  try {
    // Check if we're in Electron environment
    if (typeof window !== 'undefined' && window.electronAPI) {
      health.database = true; // Assume database is working if IPC is available
    }

    // Check config store
    const configStore = getConfigStoreBrowser();
    health.configStore = configStore.isInitialized();

    health.overall = health.configStore;
  } catch (error) {
    console.error('Background systems health check failed:', error);
  }

  return health;
}
