// components/background/index.ts

// Database Manager
export { 
  DatabaseManager, 
  getDatabaseManager, 
  closeDatabaseManager,
  type DatabaseConfig,
  type Migration 
} from './database-manager';

// Configuration Store
export { 
  ConfigStore, 
  getConfigStore,
  type ProjectConfig,
  type NamingConventions,
  type CodeArchitecture,
  type StyleGuide,
  type GlobalSettings 
} from './config-store';

// Default Configurations
export {
  DEFAULT_NAMING_CONVENTIONS,
  DEFAULT_CODE_ARCHITECTURE,
  DEFAULT_STYLE_GUIDE,
  TYPESCRIPT_NAMING_CONVENTIONS,
  REACT_CODE_ARCHITECTURE,
  NEXTJS_STYLE_GUIDE,
  PROJECT_TEMPLATES,
  getProjectTemplate,
  createDefaultProjectConfig
} from './default-configs';

// Background System Initialization
export async function initializeBackgroundSystems(): Promise<void> {
  try {
    console.log('Initializing background systems...');
    
    // Initialize database manager
    const dbManager = getDatabaseManager();
    await dbManager.initialize();
    
    // Initialize config store
    const configStore = getConfigStore();
    await configStore.initialize();
    
    console.log('Background systems initialized successfully');
  } catch (error) {
    console.error('Failed to initialize background systems:', error);
    throw error;
  }
}

// Background System Cleanup
export function cleanupBackgroundSystems(): void {
  try {
    console.log('Cleaning up background systems...');
    closeDatabaseManager();
    console.log('Background systems cleaned up successfully');
  } catch (error) {
    console.error('Failed to cleanup background systems:', error);
  }
}

// Health Check
export async function checkBackgroundSystemsHealth(): Promise<{
  database: boolean;
  configStore: boolean;
  overall: boolean;
}> {
  const health = {
    database: false,
    configStore: false,
    overall: false
  };

  try {
    // Check database
    const dbManager = getDatabaseManager();
    health.database = dbManager.isInitialized();

    // Check config store
    const configStore = getConfigStore();
    // Try a simple query to verify it's working
    await configStore.getGlobalSetting('system', 'healthCheck');
    health.configStore = true;

    health.overall = health.database && health.configStore;
  } catch (error) {
    console.error('Background systems health check failed:', error);
  }

  return health;
}
